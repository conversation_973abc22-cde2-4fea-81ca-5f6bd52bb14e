#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BMN模型推理脚本
用于时间动作定位任务的完整推理流程
"""

import os
import sys
import json
import argparse
import subprocess
import numpy as np
from pathlib import Path
from typing import List, Dict, Tuple, Optional
import math

# 添加当前目录和mmaction2到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)
mmaction2_path = os.path.join(current_dir, '..', 'mmaction2')
sys.path.insert(0, mmaction2_path)

def check_dependencies():
    """检查依赖环境"""
    print("🔍 检查推理环境...")
    
    try:
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
        if torch.cuda.is_available():
            print(f"✅ CUDA可用: {torch.cuda.get_device_name(0)}")
        else:
            print("⚠️  使用CPU推理")
    except ImportError:
        print("❌ PyTorch未安装")
        return False
    
    try:
        import mmaction
        print(f"✅ MMAction2: {mmaction.__version__}")
    except ImportError:
        print("❌ MMAction2未安装")
        return False
    
    try:
        import cv2
        print(f"✅ OpenCV: {cv2.__version__}")
    except ImportError:
        print("❌ OpenCV未安装")
        return False
    
    return True

def find_best_checkpoint(work_dir: str) -> Optional[str]:
    """查找最佳模型权重文件"""
    work_path = Path(work_dir)
    
    if not work_path.exists():
        print(f"❌ 工作目录不存在: {work_dir}")
        return None
    
    # 优先查找best模型
    best_files = list(work_path.glob("best_*.pth"))
    if best_files:
        best_file = sorted(best_files, key=lambda x: x.stat().st_mtime)[-1]  # 最新的best文件
        print(f"✅ 找到最佳模型: {best_file}")
        return str(best_file)
    
    # 查找最新的epoch模型
    epoch_files = list(work_path.glob("epoch_*.pth"))
    if epoch_files:
        # 按epoch数字排序，取最大的
        epoch_nums = []
        for f in epoch_files:
            try:
                num = int(f.stem.split('_')[1])
                epoch_nums.append((num, f))
            except:
                continue
        
        if epoch_nums:
            latest_epoch_file = max(epoch_nums, key=lambda x: x[0])[1]
            print(f"✅ 找到最新模型: {latest_epoch_file}")
            return str(latest_epoch_file)
    
    print(f"❌ 在 {work_dir} 中未找到模型文件")
    return None

def get_video_info(video_path: str) -> Dict:
    """获取视频信息"""
    try:
        import cv2
        cap = cv2.VideoCapture(video_path)
        
        if not cap.isOpened():
            raise ValueError(f"无法打开视频文件: {video_path}")
        
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        duration = frame_count / fps if fps > 0 else 0
        
        cap.release()
        
        info = {
            'fps': fps,
            'frame_count': frame_count,
            'duration_second': duration,
            'duration_frame': frame_count
        }
        
        print(f"📹 视频信息:")
        print(f"   时长: {duration:.2f}秒")
        print(f"   帧数: {frame_count}")
        print(f"   帧率: {fps:.2f} FPS")
        
        return info
        
    except Exception as e:
        print(f"❌ 获取视频信息失败: {e}")
        return {}

def split_video_segments(video_path: str, segment_duration: float = 60.0, overlap_duration: float = 10.0, output_dir: str = None) -> List[Dict]:
    """
    将长视频分割为较短的片段以提高推理准确度

    Args:
        video_path: 输入视频路径
        segment_duration: 每个片段的时长（秒）
        overlap_duration: 片段间重叠时长（秒）
        output_dir: 输出目录

    Returns:
        List[Dict]: 分割片段信息列表
    """
    print(f"\n🔪 分割视频以优化推理准确度...")
    print(f"   片段时长: {segment_duration}秒")
    print(f"   重叠时长: {overlap_duration}秒")

    try:
        # 获取视频信息
        video_info = get_video_info(video_path)
        if not video_info:
            return []

        total_duration = video_info['duration_second']
        video_name = Path(video_path).stem

        # 如果视频较短，不需要分割
        if total_duration <= segment_duration * 1.5:
            print(f"   视频时长 {total_duration:.1f}秒，无需分割")
            return [{
                'segment_path': video_path,
                'start_time': 0.0,
                'end_time': total_duration,
                'segment_index': 0,
                'is_original': True
            }]

        # 创建输出目录
        if output_dir is None:
            output_dir = os.path.join(os.path.dirname(video_path), 'segments')
        os.makedirs(output_dir, exist_ok=True)

        segments = []
        segment_index = 0
        start_time = 0.0

        while start_time < total_duration:
            end_time = min(start_time + segment_duration, total_duration)

            # 生成片段文件名
            segment_filename = f"{video_name}_segment_{segment_index:03d}_{start_time:.1f}s-{end_time:.1f}s.mp4"
            segment_path = os.path.join(output_dir, segment_filename)

            # 使用ffmpeg分割视频
            cmd = [
                'ffmpeg', '-y',  # -y 覆盖输出文件
                '-i', video_path,
                '-ss', str(start_time),
                '-t', str(end_time - start_time),
                '-c', 'copy',  # 复制编码，避免重新编码
                '-avoid_negative_ts', 'make_zero',
                segment_path
            ]

            print(f"   分割片段 {segment_index + 1}: {start_time:.1f}s - {end_time:.1f}s")

            try:
                result = subprocess.run(cmd, capture_output=True, text=True, check=True)

                segments.append({
                    'segment_path': segment_path,
                    'start_time': start_time,
                    'end_time': end_time,
                    'segment_index': segment_index,
                    'is_original': False
                })

                segment_index += 1

            except subprocess.CalledProcessError as e:
                print(f"   ⚠️  分割片段失败: {e}")
                print(f"   stderr: {e.stderr}")
                continue

            # 计算下一个片段的开始时间（考虑重叠）
            if end_time >= total_duration:
                break
            start_time = end_time - overlap_duration

        print(f"✅ 视频分割完成，共生成 {len(segments)} 个片段")
        return segments

    except Exception as e:
        print(f"❌ 视频分割失败: {e}")
        return []

def extract_slowonly_features(video_path: str, output_dir: str) -> Optional[str]:
    """提取SlowOnly特征 - 使用与训练阶段一致的方法"""
    print(f"\n🎬 提取SlowOnly特征...")
    print(f"   输入视频: {video_path}")

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 特征文件路径
    video_name = Path(video_path).stem
    feature_file = os.path.join(output_dir, f"{video_name}.csv")

    if os.path.exists(feature_file):
        print(f"✅ 特征文件已存在: {feature_file}")
        return feature_file

    try:
        print(f"❌ 推理阶段的特征提取功能尚未完全实现")
        print(f"   当前版本使用训练阶段预提取的特征")
        print(f"   请确保特征文件已通过训练流程生成")

        # 尝试从训练数据目录查找对应的特征文件
        training_feature_dir = "../../../data/MultiClassTAD/features_slowonly_reconstructed"
        training_feature_file = os.path.join(training_feature_dir, f"{video_name}.csv")

        if os.path.exists(training_feature_file):
            print(f"✅ 找到训练阶段的特征文件: {training_feature_file}")
            # 复制到推理输出目录
            import shutil
            shutil.copy2(training_feature_file, feature_file)
            print(f"✅ 特征文件已复制到: {feature_file}")
            return feature_file
        else:
            print(f"❌ 未找到对应的训练特征文件: {training_feature_file}")
            print(f"   请先运行训练流程生成特征文件")
            return None

    except Exception as e:
        print(f"❌ 特征提取异常: {e}")
        import traceback
        traceback.print_exc()
        return None

def load_bmn_model(config_path: str, checkpoint_path: str):
    """加载BMN模型"""
    print(f"\n🤖 加载BMN模型...")
    print(f"   配置文件: {config_path}")
    print(f"   权重文件: {checkpoint_path}")
    
    try:
        from mmengine.config import Config
        from mmaction.apis import init_recognizer
        
        # 加载配置
        config = Config.fromfile(config_path)
        
        # 初始化模型
        model = init_recognizer(config, checkpoint_path, device='cuda:0')
        
        print(f"✅ 模型加载成功")
        return model, config
        
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def prepare_inference_data(feature_file: str, video_info: Dict) -> Dict:
    """准备推理数据"""
    print(f"\n📊 准备推理数据...")

    try:
        # 读取特征文件并检查维度
        import pandas as pd
        features_df = pd.read_csv(feature_file)

        # 检查特征文件的格式：应该是 (时间步, 特征维度) 或 (特征维度, 时间步)
        shape = features_df.shape
        print(f"   原始特征形状: {shape}")

        # 判断哪个维度是特征维度（应该是2048），哪个是时间维度
        if shape[1] == 2048:  # (时间步, 2048)
            actual_temporal_dim = shape[0]
            feature_dim = shape[1]
            features_array = features_df.values  # (时间步, 2048)
        elif shape[0] == 2048:  # (2048, 时间步) - 需要转置
            actual_temporal_dim = shape[1]
            feature_dim = shape[0]
            features_array = features_df.values.T  # 转置为 (时间步, 2048)
            print(f"   转置特征矩阵: ({shape[0]}, {shape[1]}) -> ({actual_temporal_dim}, {feature_dim})")
        else:
            raise ValueError(f"特征维度不正确，期望包含2048维特征，但得到形状: {shape}")

        # 如果时间维度不是100，需要重新采样到100
        target_temporal_dim = 100
        if actual_temporal_dim != target_temporal_dim:
            print(f"   重新采样特征从 {actual_temporal_dim} 到 {target_temporal_dim} 个时间步...")

            # 使用线性插值重新采样
            from scipy.interpolate import interp1d
            import numpy as np

            # 原始时间索引
            original_indices = np.linspace(0, actual_temporal_dim-1, actual_temporal_dim)
            # 目标时间索引
            target_indices = np.linspace(0, actual_temporal_dim-1, target_temporal_dim)

            # 对每个特征维度进行插值
            resampled_features = np.zeros((target_temporal_dim, feature_dim))
            for i in range(feature_dim):
                f = interp1d(original_indices, features_array[:, i], kind='linear')
                resampled_features[:, i] = f(target_indices)

            # 保存重新采样的特征
            # 注意：BMN期望的格式是 [batch_size, feat_dim, temporal_dim]
            # 所以我们需要转置特征矩阵为 (feat_dim, temporal_dim) 格式保存
            resampled_features_transposed = resampled_features.T  # (2048, 100)

            resampled_file = feature_file.replace('.csv', '_resampled.csv')
            header = ','.join([f't{i}' for i in range(target_temporal_dim)])
            np.savetxt(resampled_file, resampled_features_transposed, delimiter=',', header=header, comments='')

            print(f"   重新采样完成，保存到: {resampled_file}")
            print(f"   保存格式: ({feature_dim}, {target_temporal_dim}) - 符合BMN期望")

            # 使用重新采样的特征文件
            feature_file = resampled_file
        else:
            # 如果时间维度已经是100，但格式可能不对，检查并修复
            if features_array.shape == (target_temporal_dim, feature_dim):
                # 需要转置为 (feat_dim, temporal_dim) 格式
                print(f"   转置特征格式为BMN期望的格式...")
                features_transposed = features_array.T  # (2048, 100)

                corrected_file = feature_file.replace('.csv', '_corrected.csv')
                header = ','.join([f't{i}' for i in range(target_temporal_dim)])
                np.savetxt(corrected_file, features_transposed, delimiter=',', header=header, comments='')

                print(f"   格式修正完成，保存到: {corrected_file}")
                print(f"   保存格式: ({feature_dim}, {target_temporal_dim}) - 符合BMN期望")

                # 使用修正的特征文件
                feature_file = corrected_file

        # 构建数据字典，模拟标注格式
        data = {
            'feature_path': feature_file,
            'video_name': Path(feature_file).stem.replace('_resampled', ''),
            'duration_second': video_info.get('duration_second', 10.0),
            'duration_frame': video_info.get('duration_frame', 250),
            'feature_frame': target_temporal_dim,  # 使用目标时间维度
            'annotations': []  # 推理时不需要标注
        }

        print(f"✅ 数据准备完成")
        print(f"   特征文件: {feature_file}")
        print(f"   视频时长: {data['duration_second']:.2f}秒")

        return data

    except Exception as e:
        print(f"❌ 数据准备失败: {e}")
        import traceback
        traceback.print_exc()
        return {}

def run_bmn_inference(model, config, data: Dict) -> Optional[Dict]:
    """执行BMN推理"""
    print(f"\n🚀 执行BMN推理...")
    
    try:
        from mmengine.dataset import Compose
        from mmaction.utils import register_all_modules
        
        # 注册所有模块
        register_all_modules()
        
        # 导入自定义变换
        import sys
        import os
        # 添加training/configs目录到Python路径以导入fix_transforms
        training_configs_path = os.path.join(os.path.dirname(__file__), '..', '..', 'training', 'configs')
        if training_configs_path not in sys.path:
            sys.path.insert(0, training_configs_path)
        import fix_transforms  # noqa: F401
        
        # 构建测试管道
        test_pipeline = Compose(config.test_pipeline)
        
        # 处理数据
        processed_data = test_pipeline(data)
        
        # 转换为批次格式
        from mmengine.dataset import pseudo_collate
        batch_data = pseudo_collate([processed_data])
        
        # 执行推理
        import torch
        with torch.no_grad():
            results = model.test_step(batch_data)
        
        if results and len(results) > 0:
            result = results[0]
            print(f"✅ 推理完成")
            return result
        else:
            print(f"❌ 推理结果为空")
            return None
            
    except Exception as e:
        print(f"❌ 推理失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def parse_bmn_results(result, video_info: Dict, confidence_threshold: float = 0.5) -> List[Dict]:
    """解析BMN推理结果"""
    print(f"\n📋 解析推理结果...")
    print(f"   置信度阈值: {confidence_threshold}")

    try:
        proposals = []

        # 调试：打印结果结构
        print(f"   结果类型: {type(result)}")
        if isinstance(result, dict):
            print(f"   结果键: {list(result.keys())}")
        else:
            print(f"   结果属性: {dir(result)}")

        # 处理字典格式的结果
        if isinstance(result, dict):
            # 查找可能的预测结果键
            pred_data = None
            if 'pred_instances' in result:
                pred_data = result['pred_instances']
            elif 'predictions' in result:
                pred_data = result['predictions']
            elif 'pred' in result:
                pred_data = result['pred']
            elif 'proposal_list' in result:
                # BMN特有的输出格式
                proposal_list = result['proposal_list']
                print(f"   找到proposal_list，长度: {len(proposal_list)}")

                # 调试：查看前几个proposal的格式
                if len(proposal_list) > 0:
                    print(f"   第一个proposal类型: {type(proposal_list[0])}")
                    print(f"   第一个proposal内容: {proposal_list[0]}")
                    if len(proposal_list) > 1:
                        print(f"   第二个proposal内容: {proposal_list[1]}")

                # 解析proposal_list格式
                duration = video_info.get('duration_second', 10.0)

                for i, proposal in enumerate(proposal_list):
                    if isinstance(proposal, dict):
                        # BMN的proposal格式: {'score': float, 'segment': [start, end]}
                        if i < 3:  # 只打印前3个的调试信息
                            print(f"   proposal {i} 字典键: {list(proposal.keys())}")

                        score = proposal.get('score', 0)
                        segment = proposal.get('segment', [0, 0])

                        if len(segment) >= 2:
                            # segment中的时间已经是绝对时间（秒）
                            start_time = float(segment[0])
                            end_time = float(segment[1])
                        else:
                            start_time = 0.0
                            end_time = 0.0

                        if score >= confidence_threshold:
                            proposals.append({
                                'start_time': start_time,
                                'end_time': end_time,
                                'confidence': float(score),
                                'duration': float(end_time - start_time),
                                'proposal_id': i
                            })
                    elif isinstance(proposal, (list, tuple)) and len(proposal) >= 3:
                        # 假设格式为 [start, end, score]
                        if i < 3:  # 只打印前3个的调试信息
                            print(f"   proposal {i} 列表内容: {proposal}")
                        start_time = proposal[0] * duration
                        end_time = proposal[1] * duration
                        score = proposal[2]

                        if score >= confidence_threshold:
                            proposals.append({
                                'start_time': float(start_time),
                                'end_time': float(end_time),
                                'confidence': float(score),
                                'duration': float(end_time - start_time),
                                'proposal_id': i
                            })
                    elif hasattr(proposal, '__len__') and len(proposal) >= 3:
                        # 处理numpy数组或tensor
                        if i < 3:  # 只打印前3个的调试信息
                            print(f"   proposal {i} 数组内容: {proposal}")
                        start_time = float(proposal[0]) * duration
                        end_time = float(proposal[1]) * duration
                        score = float(proposal[2])

                        if score >= confidence_threshold:
                            proposals.append({
                                'start_time': float(start_time),
                                'end_time': float(end_time),
                                'confidence': float(score),
                                'duration': float(end_time - start_time),
                                'proposal_id': i
                            })
                    else:
                        if i < 3:  # 只打印前3个的调试信息
                            print(f"   proposal {i} 未知格式: {type(proposal)} - {proposal}")

                print(f"   从proposal_list解析到 {len(proposals)} 个有效提案")
                # 跳过后续的pred_data处理
                pred_data = None
            else:
                # 直接从字典中查找proposals和scores
                if 'proposals' in result and 'scores' in result:
                    pred_data = result
                else:
                    print(f"   可用的键: {list(result.keys())}")

            if pred_data is not None:
                print(f"   找到预测数据，类型: {type(pred_data)}")

                # 提取proposals和scores
                proposals_data = None
                scores_data = None

                if hasattr(pred_data, 'proposals') and hasattr(pred_data, 'scores'):
                    proposals_data = pred_data.proposals.cpu().numpy()
                    scores_data = pred_data.scores.cpu().numpy()
                elif isinstance(pred_data, dict):
                    if 'proposals' in pred_data and 'scores' in pred_data:
                        proposals_data = pred_data['proposals']
                        scores_data = pred_data['scores']
                        if hasattr(proposals_data, 'cpu'):
                            proposals_data = proposals_data.cpu().numpy()
                        if hasattr(scores_data, 'cpu'):
                            scores_data = scores_data.cpu().numpy()

                if proposals_data is not None and scores_data is not None:
                    print(f"   原始提案数量: {len(proposals_data)}")
                    print(f"   提案形状: {proposals_data.shape}")
                    print(f"   分数形状: {scores_data.shape}")
                    print(f"   分数范围: {scores_data.min():.4f} - {scores_data.max():.4f}")

                    duration = video_info.get('duration_second', 10.0)

                    for i, (proposal, score) in enumerate(zip(proposals_data, scores_data)):
                        if score >= confidence_threshold:
                            # 将相对时间转换为绝对时间
                            start_time = proposal[0] * duration
                            end_time = proposal[1] * duration

                            proposals.append({
                                'start_time': float(start_time),
                                'end_time': float(end_time),
                                'confidence': float(score),
                                'duration': float(end_time - start_time),
                                'proposal_id': i
                            })

                    # 显示所有分数的统计信息
                    print(f"   满足阈值的提案: {len(proposals)} / {len(scores_data)}")
                    if len(scores_data) > 0:
                        print(f"   前10个分数: {scores_data[:10]}")
                else:
                    print(f"   ❌ 未找到proposals或scores数据")
            else:
                print(f"   ❌ 未找到预测数据")

        # 处理对象格式的结果
        elif hasattr(result, 'pred_instances'):
            pred_instances = result.pred_instances
            print(f"   pred_instances类型: {type(pred_instances)}")

            # 获取预测的边界框和分数
            if hasattr(pred_instances, 'proposals') and hasattr(pred_instances, 'scores'):
                proposals_data = pred_instances.proposals.cpu().numpy()
                scores_data = pred_instances.scores.cpu().numpy()

                print(f"   原始提案数量: {len(proposals_data)}")
                print(f"   提案形状: {proposals_data.shape}")
                print(f"   分数形状: {scores_data.shape}")
                print(f"   分数范围: {scores_data.min():.4f} - {scores_data.max():.4f}")

                duration = video_info.get('duration_second', 10.0)

                for i, (proposal, score) in enumerate(zip(proposals_data, scores_data)):
                    if score >= confidence_threshold:
                        # 将相对时间转换为绝对时间
                        start_time = proposal[0] * duration
                        end_time = proposal[1] * duration

                        proposals.append({
                            'start_time': float(start_time),
                            'end_time': float(end_time),
                            'confidence': float(score),
                            'duration': float(end_time - start_time),
                            'proposal_id': i
                        })

                # 显示所有分数的统计信息
                print(f"   满足阈值的提案: {len(proposals)} / {len(scores_data)}")
                if len(scores_data) > 0:
                    print(f"   前10个分数: {scores_data[:10]}")
            else:
                print(f"   ❌ pred_instances缺少proposals或scores属性")
        else:
            print(f"   ❌ 结果格式不支持")

        # 按置信度排序
        proposals.sort(key=lambda x: x['confidence'], reverse=True)

        print(f"✅ 解析完成，找到 {len(proposals)} 个有效提案")

        # 显示前几个提案
        for i, prop in enumerate(proposals[:5]):
            print(f"   提案 {i+1}: {prop['start_time']:.2f}s - {prop['end_time']:.2f}s "
                  f"(置信度: {prop['confidence']:.3f})")

        return proposals

    except Exception as e:
        print(f"❌ 结果解析失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def merge_segment_results(segment_results: List[Dict], overlap_duration: float = 10.0) -> List[Dict]:
    """
    合并多个视频片段的推理结果，处理重叠区域

    Args:
        segment_results: 每个片段的结果列表
        overlap_duration: 重叠时长（秒）

    Returns:
        List[Dict]: 合并后的提案列表
    """
    print(f"\n🔗 合并片段推理结果...")
    print(f"   片段数量: {len(segment_results)}")

    if not segment_results:
        return []

    # 如果只有一个片段，直接返回
    if len(segment_results) == 1:
        return segment_results[0].get('proposals', [])

    all_proposals = []

    for segment_result in segment_results:
        segment_info = segment_result['segment_info']
        proposals = segment_result.get('proposals', [])

        # 将片段内的相对时间转换为全局绝对时间
        for proposal in proposals:
            global_start = segment_info['start_time'] + proposal['start_time']
            global_end = segment_info['start_time'] + proposal['end_time']

            # 创建全局提案
            global_proposal = proposal.copy()
            global_proposal['start_time'] = global_start
            global_proposal['end_time'] = global_end
            global_proposal['segment_index'] = segment_info['segment_index']
            global_proposal['original_start'] = proposal['start_time']
            global_proposal['original_end'] = proposal['end_time']

            all_proposals.append(global_proposal)

    print(f"   合并前提案总数: {len(all_proposals)}")

    # 处理重叠区域的重复提案
    if overlap_duration > 0:
        all_proposals = remove_overlapping_proposals(all_proposals, overlap_duration)

    # 按置信度排序
    all_proposals.sort(key=lambda x: x['confidence'], reverse=True)

    print(f"✅ 合并完成，最终提案数: {len(all_proposals)}")

    return all_proposals

def remove_overlapping_proposals(proposals: List[Dict], overlap_threshold: float = 10.0) -> List[Dict]:
    """
    移除重叠区域的重复提案

    Args:
        proposals: 提案列表
        overlap_threshold: 重叠阈值（秒）

    Returns:
        List[Dict]: 去重后的提案列表
    """
    if not proposals:
        return []

    # 按开始时间排序
    proposals.sort(key=lambda x: x['start_time'])

    filtered_proposals = []

    for current_proposal in proposals:
        is_duplicate = False

        # 检查是否与已有提案重叠
        for existing_proposal in filtered_proposals:
            # 计算时间重叠
            overlap_start = max(current_proposal['start_time'], existing_proposal['start_time'])
            overlap_end = min(current_proposal['end_time'], existing_proposal['end_time'])
            overlap_duration = max(0, overlap_end - overlap_start)

            # 计算重叠比例
            current_duration = current_proposal['end_time'] - current_proposal['start_time']
            existing_duration = existing_proposal['end_time'] - existing_proposal['start_time']

            overlap_ratio_current = overlap_duration / current_duration if current_duration > 0 else 0
            overlap_ratio_existing = overlap_duration / existing_duration if existing_duration > 0 else 0

            # 如果重叠比例较高，认为是重复提案
            if overlap_ratio_current > 0.5 or overlap_ratio_existing > 0.5:
                # 保留置信度更高的提案
                if current_proposal['confidence'] > existing_proposal['confidence']:
                    # 替换现有提案
                    filtered_proposals.remove(existing_proposal)
                    break
                else:
                    # 跳过当前提案
                    is_duplicate = True
                    break

        if not is_duplicate:
            filtered_proposals.append(current_proposal)

    print(f"   去重后提案数: {len(filtered_proposals)}")
    return filtered_proposals

def segment_video(video_path: str, proposals: List[Dict], output_dir: str) -> List[str]:
    """根据提案分割视频"""
    print(f"\n✂️  分割视频...")
    print(f"   输入视频: {video_path}")
    print(f"   输出目录: {output_dir}")
    print(f"   提案数量: {len(proposals)}")

    os.makedirs(output_dir, exist_ok=True)

    segmented_files = []
    video_name = Path(video_path).stem

    for i, proposal in enumerate(proposals):
        try:
            start_time = proposal['start_time']
            end_time = proposal['end_time']
            confidence = proposal['confidence']

            # 生成输出文件名
            output_filename = f"segment_{start_time:.2f}_{end_time:.2f}_action_{confidence:.3f}.mp4"
            output_path = os.path.join(output_dir, output_filename)

            # 使用ffmpeg分割视频
            cmd = [
                'ffmpeg', '-y',  # -y 覆盖输出文件
                '-i', video_path,
                '-ss', str(start_time),
                '-t', str(end_time - start_time),
                '-c', 'copy',  # 复制编码，速度快
                '-avoid_negative_ts', 'make_zero',
                output_path
            ]

            print(f"   分割片段 {i+1}: {start_time:.2f}s - {end_time:.2f}s")

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)

            if result.returncode == 0 and os.path.exists(output_path):
                segmented_files.append(output_path)

                # 创建对应的标签文件
                label_file = output_path.replace('.mp4', '.json')
                label_data = {
                    'video_file': output_filename,
                    'original_video': os.path.basename(video_path),
                    'start_time': start_time,
                    'end_time': end_time,
                    'duration': end_time - start_time,
                    'confidence': confidence,
                    'action_class': 'action',  # BMN不预测具体类别
                    'proposal_id': proposal.get('proposal_id', i)
                }

                with open(label_file, 'w', encoding='utf-8') as f:
                    json.dump(label_data, f, indent=2, ensure_ascii=False)

                print(f"     ✅ 成功: {output_filename}")
            else:
                print(f"     ❌ 失败: ffmpeg错误")
                if result.stderr:
                    print(f"        错误信息: {result.stderr[:200]}")

        except subprocess.TimeoutExpired:
            print(f"     ❌ 超时: 片段 {i+1}")
        except Exception as e:
            print(f"     ❌ 异常: {e}")

    print(f"✅ 视频分割完成，成功分割 {len(segmented_files)} 个片段")
    return segmented_files

def save_results(proposals: List[Dict], output_file: str):
    """保存推理结果"""
    print(f"\n💾 保存推理结果...")

    try:
        results_data = {
            'total_proposals': len(proposals),
            'proposals': proposals,
            'metadata': {
                'model': 'BMN',
                'task': 'temporal_action_localization',
                'timestamp': str(np.datetime64('now'))
            }
        }

        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results_data, f, indent=2, ensure_ascii=False)

        print(f"✅ 结果已保存: {output_file}")

    except Exception as e:
        print(f"❌ 保存失败: {e}")

def main():
    parser = argparse.ArgumentParser(description='BMN模型推理脚本')
    parser.add_argument('--video', required=True, help='输入视频文件路径')
    parser.add_argument('--config', default='../../training/configs/bmn_multiclass_tad_config.py', help='BMN配置文件')
    parser.add_argument('--work-dir', default='../../../work_dirs/bmn_multiclass_tad_slowonly', help='模型工作目录')
    parser.add_argument('--output-dir', default='./inference_results', help='输出目录')
    parser.add_argument('--confidence-threshold', type=float, default=0.5, help='置信度阈值')
    parser.add_argument('--extract-features', action='store_true', help='是否提取特征（如果特征文件不存在）')
    parser.add_argument('--enable-segmentation', action='store_true', help='启用视频分割优化（推荐用于长视频）')
    parser.add_argument('--segment-duration', type=float, default=60.0, help='视频片段时长（秒）')
    parser.add_argument('--overlap-duration', type=float, default=10.0, help='片段重叠时长（秒）')

    args = parser.parse_args()

    print("=" * 80)
    print("🎯 BMN时间动作定位推理")
    print("=" * 80)

    # 检查环境
    if not check_dependencies():
        return

    # 检查输入文件
    if not os.path.exists(args.video):
        print(f"❌ 视频文件不存在: {args.video}")
        return

    if not os.path.exists(args.config):
        print(f"❌ 配置文件不存在: {args.config}")
        return

    # 查找模型权重
    checkpoint_path = find_best_checkpoint(args.work_dir)
    if not checkpoint_path:
        return

    # 获取视频信息
    video_info = get_video_info(args.video)
    if not video_info:
        return

    # 检查是否需要视频分割优化
    video_duration = video_info.get('duration_second', 0)
    use_segmentation = args.enable_segmentation or (video_duration > 120)  # 超过2分钟自动启用

    if use_segmentation:
        print(f"\n🚀 启用视频分割优化（视频时长: {video_duration:.1f}秒）")

        # 分割视频
        segments_dir = os.path.join(args.output_dir, 'temp_segments')
        segments = split_video_segments(
            args.video,
            args.segment_duration,
            args.overlap_duration,
            segments_dir
        )

        if not segments:
            print("❌ 视频分割失败，回退到原始方法")
            use_segmentation = False
        else:
            # 加载模型（只加载一次）
            model, config = load_bmn_model(args.config, checkpoint_path)
            if not model:
                return

            # 对每个片段进行推理
            segment_results = []

            for i, segment_info in enumerate(segments):
                print(f"\n📊 处理片段 {i+1}/{len(segments)}: {segment_info['start_time']:.1f}s - {segment_info['end_time']:.1f}s")

                # 提取片段特征
                segment_name = Path(segment_info['segment_path']).stem
                feature_dir = os.path.join(args.output_dir, 'features')
                feature_file = os.path.join(feature_dir, f"{segment_name}.csv")

                if not os.path.exists(feature_file):
                    if args.extract_features:
                        feature_file = extract_slowonly_features(segment_info['segment_path'], feature_dir)
                        if not feature_file:
                            print(f"   ⚠️  片段 {i+1} 特征提取失败，跳过")
                            continue
                    else:
                        print(f"   ⚠️  片段 {i+1} 特征文件不存在，跳过")
                        continue

                # 获取片段视频信息
                segment_video_info = get_video_info(segment_info['segment_path'])
                if not segment_video_info:
                    print(f"   ⚠️  片段 {i+1} 视频信息获取失败，跳过")
                    continue

                # 准备推理数据
                data = prepare_inference_data(feature_file, segment_video_info)
                if not data:
                    print(f"   ⚠️  片段 {i+1} 数据准备失败，跳过")
                    continue

                # 执行推理
                result = run_bmn_inference(model, config, data)
                if not result:
                    print(f"   ⚠️  片段 {i+1} 推理失败，跳过")
                    continue

                # 解析结果
                segment_proposals = parse_bmn_results(result, segment_video_info, args.confidence_threshold)

                segment_results.append({
                    'segment_info': segment_info,
                    'proposals': segment_proposals
                })

                print(f"   ✅ 片段 {i+1} 完成，找到 {len(segment_proposals)} 个提案")

            # 合并所有片段的结果
            proposals = merge_segment_results(segment_results, args.overlap_duration)

            # 清理临时片段文件
            if not segment_info.get('is_original', False):
                try:
                    import shutil
                    if os.path.exists(segments_dir):
                        shutil.rmtree(segments_dir)
                        print(f"🗑️  清理临时文件: {segments_dir}")
                except Exception as e:
                    print(f"⚠️  清理临时文件失败: {e}")

    if not use_segmentation:
        # 原始单视频推理方法
        print(f"\n📊 使用原始推理方法（视频时长: {video_duration:.1f}秒）")

        # 准备特征文件
        video_name = Path(args.video).stem
        feature_dir = os.path.join(args.output_dir, 'features')
        feature_file = os.path.join(feature_dir, f"{video_name}.csv")

        if not os.path.exists(feature_file):
            if args.extract_features:
                feature_file = extract_slowonly_features(args.video, feature_dir)
                if not feature_file:
                    print("❌ 特征提取失败，无法继续推理")
                    return
            else:
                print(f"❌ 特征文件不存在: {feature_file}")
                print("   请使用 --extract-features 参数自动提取特征，或手动提取特征文件")
                return

        # 加载模型
        model, config = load_bmn_model(args.config, checkpoint_path)
        if not model:
            return

        # 准备推理数据
        data = prepare_inference_data(feature_file, video_info)
        if not data:
            return

        # 执行推理
        result = run_bmn_inference(model, config, data)
        if not result:
            return

        # 解析结果
        proposals = parse_bmn_results(result, video_info, args.confidence_threshold)

    if not proposals:
        print("⚠️  未找到满足置信度阈值的提案")
        return

    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)

    # 保存推理结果
    video_name = Path(args.video).stem
    results_file = os.path.join(args.output_dir, f"{video_name}_results.json")
    save_results(proposals, results_file)

    # 分割视频
    segments_dir = os.path.join(args.output_dir, 'segments')
    segmented_files = segment_video(args.video, proposals, segments_dir)

    # 总结
    print("\n" + "=" * 80)
    print("🎉 推理完成!")
    print(f"   输入视频: {args.video}")
    print(f"   视频时长: {video_duration:.1f}秒")
    print(f"   优化方法: {'视频分割优化' if use_segmentation else '原始方法'}")
    if use_segmentation:
        print(f"   处理片段: {len(segments)} 个")
    print(f"   找到提案: {len(proposals)} 个")
    print(f"   分割片段: {len(segmented_files)} 个")
    print(f"   输出目录: {args.output_dir}")
    print(f"   结果文件: {results_file}")
    if use_segmentation:
        print("   💡 使用了视频分割优化，提高了推理准确度")
    print("=" * 80)

if __name__ == '__main__':
    main()
