ann_file = 'data/MultiClassTAD/video_list.txt'
data_root = 'data'
dataset_type = 'VideoDataset'
default_hooks = dict(
    checkpoint=dict(interval=3, save_best='auto', type='CheckpointHook'),
    logger=dict(ignore_last=False, interval=20, type='LoggerHook'),
    param_scheduler=dict(type='ParamSchedulerHook'),
    runtime_info=dict(type='RuntimeInfoHook'),
    sampler_seed=dict(type='DistSamplerSeedHook'),
    sync_buffers=dict(type='SyncBuffersHook'),
    timer=dict(type='IterTimerHook'))
default_scope = 'mmaction'
env_cfg = dict(
    cudnn_benchmark=False,
    dist_cfg=dict(backend='nccl'),
    mp_cfg=dict(mp_start_method='fork', opencv_num_threads=0))
file_client_args = dict(io_backend='disk')
launcher = 'none'
load_from = '/home/<USER>/johnny_ws/mmaction2_ws/work_dirs/checkpoints/slowonly_r50_kinetics400.pth'
log_level = 'INFO'
log_processor = dict(by_epoch=True, type='LogProcessor', window_size=20)
model = dict(
    backbone=dict(
        conv1_kernel=(
            1,
            7,
            7,
        ),
        conv1_stride_t=1,
        depth=50,
        inflate=(
            0,
            0,
            1,
            1,
        ),
        lateral=False,
        norm_eval=False,
        pool1_stride_t=1,
        pretrained=
        'https://download.openmmlab.com/mmaction/v1.0/recognition/slowonly/slowonly_imagenet-pretrained-r50_8xb16-4x16x1-steplr-150e_kinetics400-rgb/slowonly_imagenet-pretrained-r50_8xb16-4x16x1-steplr-150e_kinetics400-rgb_20220901-e7b65fad.pth',
        type='ResNet3dSlowOnly'),
    cls_head=dict(
        average_clips='score',
        backbone_name=None,
        num_segments=None,
        spatial_type='avg',
        temporal_type='avg',
        type='FeatureHead'),
    data_preprocessor=dict(
        format_shape='NCTHW',
        mean=[
            123.675,
            116.28,
            103.53,
        ],
        std=[
            58.395,
            57.12,
            57.375,
        ],
        type='ActionDataPreprocessor'),
    type='Recognizer3D')
resume = False
test_cfg = dict(type='TestLoop')
test_dataloader = dict(
    batch_size=1,
    dataset=dict(
        ann_file=
        '/home/<USER>/johnny_ws/mmaction2_ws/data_process/inference/scripts/inference_results/features/temp_20250728T075409Z_20250728T075909Z_segment_001_50.0s-110.0s_list.txt',
        data_prefix=dict(video='/home/<USER>/johnny_ws/mmaction2_ws/data'),
        pipeline=[
            dict(io_backend='disk', type='DecordInit'),
            dict(
                clip_len=8,
                frame_interval=8,
                num_clips=1,
                test_mode=True,
                twice_sample=False,
                type='SampleFrames'),
            dict(type='DecordDecode'),
            dict(scale=(
                -1,
                224,
            ), type='Resize'),
            dict(crop_size=224, type='CenterCrop'),
            dict(input_format='NCTHW', type='FormatShape'),
            dict(type='PackActionInputs'),
        ],
        test_mode=True,
        type='VideoDataset'),
    num_workers=1,
    persistent_workers=False,
    sampler=dict(shuffle=False, type='DefaultSampler'))
test_evaluator = [
    dict(
        out_file_path=
        '/home/<USER>/johnny_ws/mmaction2_ws/data_process/inference/scripts/inference_results/features/temp_20250728T075409Z_20250728T075909Z_segment_001_50.0s-110.0s_raw/total_feats.pkl',
        type='DumpResults'),
]
test_pipeline = [
    dict(io_backend='disk', type='DecordInit'),
    dict(
        clip_len=8,
        frame_interval=8,
        num_clips=1,
        test_mode=True,
        type='SampleFrames'),
    dict(type='DecordDecode'),
    dict(scale=(
        -1,
        224,
    ), type='Resize'),
    dict(crop_size=224, type='CenterCrop'),
    dict(input_format='NCTHW', type='FormatShape'),
    dict(type='PackActionInputs'),
]
vis_backends = [
    dict(type='LocalVisBackend'),
]
visualizer = dict(
    type='ActionVisualizer', vis_backends=[
        dict(type='LocalVisBackend'),
    ])
work_dir = '/home/<USER>/johnny_ws/mmaction2_ws/data_process/inference/scripts/inference_results/features/temp_20250728T075409Z_20250728T075909Z_segment_001_50.0s-110.0s_raw/work_dir'
