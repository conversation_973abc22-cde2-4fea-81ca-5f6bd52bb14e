Collections:
  - Name: TSN
    README: configs/recognition/tsn/README.md
    Paper:
      URL: https://arxiv.org/abs/1608.00859
      Title: "Temporal Segment Networks: Towards Good Practices for Deep Action Recognition"

Models:
  - Name: tsn_imagenet-pretrained-r50_8xb32-1x1x3-100e_kinetics400-rgb
    Config: configs/recognition/tsn/tsn_imagenet-pretrained-r50_8xb32-1x1x3-100e_kinetics400-rgb.py
    In Collection: TSN
    Metadata:
      Architecture: ResNet50
      Batch Size: 32
      Epochs: 100
      FLOPs: 102.7G
      Parameters: 24.33M
      Pretrained: ImageNet
      Resolution: 224x224
      Training Data: Kinetics-400
      Training Resources: 8 GPUs
    Modality: RGB
    Results:
    - Dataset: Kinetics-400
      Task: Action Recognition
      Metrics:
        Top 1 Accuracy: 72.83
        Top 5 Accuracy: 90.65
    Training Log: https://download.openmmlab.com/mmaction/v1.0/recognition/tsn/tsn_imagenet-pretrained-r50_8xb32-1x1x3-100e_kinetics400-rgb/tsn_imagenet-pretrained-r50_8xb32-1x1x3-100e_kinetics400-rgb.log
    Weights: https://download.openmmlab.com/mmaction/v1.0/recognition/tsn/tsn_imagenet-pretrained-r50_8xb32-1x1x3-100e_kinetics400-rgb/tsn_imagenet-pretrained-r50_8xb32-1x1x3-100e_kinetics400-rgb_20220906-cd10898e.pth

  - Name: tsn_imagenet-pretrained-r50_8xb32-1x1x5-100e_kinetics400-rgb
    Config: configs/recognition/tsn/tsn_imagenet-pretrained-r50_8xb32-1x1x5-100e_kinetics400-rgb.py
    In Collection: TSN
    Metadata:
      Architecture: ResNet50
      Batch Size: 32
      Epochs: 100
      FLOPs: 102.7G
      Parameters: 24.33M
      Pretrained: ImageNet
      Resolution: 224x224
      Training Data: Kinetics-400
      Training Resources: 8 GPUs
    Modality: RGB
    Results:
    - Dataset: Kinetics-400
      Task: Action Recognition
      Metrics:
        Top 1 Accuracy: 73.80
        Top 5 Accuracy: 91.21
    Training Log: https://download.openmmlab.com/mmaction/v1.0/recognition/tsn/tsn_imagenet-pretrained-r50_8xb32-1x1x5-100e_kinetics400-rgb/tsn_imagenet-pretrained-r50_8xb32-1x1x5-100e_kinetics400-rgb.log
    Weights: https://download.openmmlab.com/mmaction/v1.0/recognition/tsn/tsn_imagenet-pretrained-r50_8xb32-1x1x5-100e_kinetics400-rgb/tsn_imagenet-pretrained-r50_8xb32-1x1x5-100e_kinetics400-rgb_20220906-65d68713.pth

  - Name: tsn_imagenet-pretrained-r50_8xb32-1x1x8-100e_kinetics400-rgb
    Alias:
      - TSN
    Config: configs/recognition/tsn/tsn_imagenet-pretrained-r50_8xb32-1x1x8-100e_kinetics400-rgb.py
    In Collection: TSN
    Metadata:
      Architecture: ResNet50
      Batch Size: 32
      Epochs: 100
      FLOPs: 102.7G
      Parameters: 24.33M
      Pretrained: ImageNet
      Resolution: 224x224
      Training Data: Kinetics-400
      Training Resources: 8 GPUs
    Modality: RGB
    Results:
    - Dataset: Kinetics-400
      Task: Action Recognition
      Metrics:
        Top 1 Accuracy: 74.12
        Top 5 Accuracy: 91.34
    Training Log: https://download.openmmlab.com/mmaction/v1.0/recognition/tsn/tsn_imagenet-pretrained-r50_8xb32-1x1x8-100e_kinetics400-rgb/tsn_imagenet-pretrained-r50_8xb32-1x1x8-100e_kinetics400-rgb.log
    Weights: https://download.openmmlab.com/mmaction/v1.0/recognition/tsn/tsn_imagenet-pretrained-r50_8xb32-1x1x8-100e_kinetics400-rgb/tsn_imagenet-pretrained-r50_8xb32-1x1x8-100e_kinetics400-rgb_20220906-2692d16c.pth

  - Name: tsn_imagenet-pretrained-r50_8xb32-dense-1x1x5-100e_kinetics400-rgb
    Config: configs/recognition/tsn/tsn_imagenet-pretrained-r50_8xb32-dense-1x1x5-100e_kinetics400-rgb.py
    In Collection: TSN
    Metadata:
      Architecture: ResNet50
      Batch Size: 32
      Epochs: 100
      FLOPs: 102.7G
      Parameters: 24.33M
      Pretrained: ImageNet
      Resolution: 224x224
      Training Data: Kinetics-400
      Training Resources: 8 GPUs
    Modality: RGB
    Results:
    - Dataset: Kinetics-400
      Task: Action Recognition
      Metrics:
        Top 1 Accuracy: 71.37
        Top 5 Accuracy: 89.67
    Training Log: https://download.openmmlab.com/mmaction/v1.0/recognition/tsn/tsn_imagenet-pretrained-r50_8xb32-dense-1x1x5-100e_kinetics400-rgb/tsn_imagenet-pretrained-r50_8xb32-dense-1x1x5-100e_kinetics400-rgb.log
    Weights: https://download.openmmlab.com/mmaction/v1.0/recognition/tsn/tsn_imagenet-pretrained-r50_8xb32-dense-1x1x5-100e_kinetics400-rgb/tsn_imagenet-pretrained-r50_8xb32-dense-1x1x5-100e_kinetics400-rgb_20220906-dcbc6e01.pth

  - Name: tsn_imagenet-pretrained-r101_8xb32-1x1x8-100e_kinetics400-rgb
    Config: configs/recognition/tsn/tsn_imagenet-pretrained-r101_8xb32-1x1x8-100e_kinetics400-rgb.py
    In Collection: TSN
    Metadata:
      Architecture: ResNet101
      Batch Size: 32
      Epochs: 100
      FLOPs: 195.8G
      Parameters: 43.32M
      Pretrained: ImageNet
      Resolution: 224x224
      Training Data: Kinetics-400
      Training Resources: 8 GPUs
    Modality: RGB
    Results:
    - Dataset: Kinetics-400
      Task: Action Recognition
      Metrics:
        Top 1 Accuracy: 75.89
        Top 5 Accuracy: 92.07
    Training Log: https://download.openmmlab.com/mmaction/v1.0/recognition/tsn/tsn_imagenet-pretrained-r101_8xb32-1x1x8-100e_kinetics400-rgb/tsn_imagenet-pretrained-r101_8xb32-1x1x8-100e_kinetics400-rgb.log
    Weights: https://download.openmmlab.com/mmaction/v1.0/recognition/tsn/tsn_imagenet-pretrained-r101_8xb32-1x1x8-100e_kinetics400-rgb/tsn_imagenet-pretrained-r101_8xb32-1x1x8-100e_kinetics400-rgb_20220906-23cff032.pth

  - Name: tsn_imagenet-pretrained-rn101-32x4d_8xb32-1x1x3-100e_kinetics400-rgb
    Config: configs/recognition/tsn/custom_backbones/tsn_imagenet-pretrained-rn101-32x4d_8xb32-1x1x3-100e_kinetics400-rgb.py
    In Collection: TSN
    Metadata:
      Architecture: ResNext101
      Batch Size: 32
      Epochs: 100
      FLOPs: 200.3G
      Parameters: 42.95M
      Pretrained: ImageNet
      Resolution: 224x224
      Training Data: Kinetics-400
      Training Resources: 8 GPUs
    Modality: RGB
    Results:
    - Dataset: Kinetics-400
      Task: Action Recognition
      Metrics:
        Top 1 Accuracy: 72.95
        Top 5 Accuracy: 90.36
    Training Log: https://download.openmmlab.com/mmaction/v1.0/recognition/tsn/tsn_imagenet-pretrained-rn101-32x4d_8xb32-1x1x3-100e_kinetics400-rgb/tsn_imagenet-pretrained-rn101-32x4d_8xb32-1x1x3-100e_kinetics400-rgb.log
    Weights: https://download.openmmlab.com/mmaction/v1.0/recognition/tsn/tsn_imagenet-pretrained-rn101-32x4d_8xb32-1x1x3-100e_kinetics400-rgb/tsn_imagenet-pretrained-rn101-32x4d_8xb32-1x1x3-100e_kinetics400-rgb_20221209-de2d5615.pth

  - Name: tsn_imagenet-pretrained-dense161_8xb32-1x1x3-100e_kinetics400-rgb
    Config: configs/recognition/tsn/custom_backbones/tsn_imagenet-pretrained-dense161_8xb32-1x1x3-100e_kinetics400-rgb.py
    In Collection: TSN
    Metadata:
      Architecture: DenseNet161
      Batch Size: 32
      Epochs: 100
      FLOPs: 194.6G
      Parameters: 27.36M
      Pretrained: ImageNet
      Resolution: 224x224
      Training Data: Kinetics-400
      Training Resources: 8 GPUs
    Modality: RGB
    Results:
    - Dataset: Kinetics-400
      Task: Action Recognition
      Metrics:
        Top 1 Accuracy: 72.07
        Top 5 Accuracy: 90.15
    Training Log: https://download.openmmlab.com/mmaction/v1.0/recognition/tsn/tsn_imagenet-pretrained-dense161_8xb32-1x1x3-100e_kinetics400-rgb/tsn_imagenet-pretrained-dense161_8xb32-1x1x3-100e_kinetics400-rgb.log
    Weights: https://download.openmmlab.com/mmaction/v1.0/recognition/tsn/tsn_imagenet-pretrained-dense161_8xb32-1x1x3-100e_kinetics400-rgb/tsn_imagenet-pretrained-dense161_8xb32-1x1x3-100e_kinetics400-rgb_20220906-5f4c0daf.pth

  - Name: tsn_imagenet-pretrained-swin-transformer_8xb32-1x1x3-100e_kinetics400-rgb
    Config: configs/recognition/tsn/custom_backbones/tsn_imagenet-pretrained-swin-transformer_8xb32-1x1x3-100e_kinetics400-rgb.py
    In Collection: TSN
    Metadata:
      Architecture: Swin-base
      Batch Size: 32
      Epochs: 100
      FLOPs: 386.7G
      Parameters: 87.15M
      Pretrained: ImageNet
      Resolution: 224x224
      Training Data: Kinetics-400
      Training Resources: 8 GPUs
    Modality: RGB
    Results:
    - Dataset: Kinetics-400
      Task: Action Recognition
      Metrics:
        Top 1 Accuracy: 77.03
        Top 5 Accuracy: 92.61
    Training Log: https://download.openmmlab.com/mmaction/v1.0/recognition/tsn/tsn_imagenet-pretrained-swin-transformer_8xb32-1x1x3-100e_kinetics400-rgb/tsn_imagenet-pretrained-swin-transformer_8xb32-1x1x3-100e_kinetics400-rgb.log
    Weights: https://download.openmmlab.com/mmaction/v1.0/recognition/tsn/tsn_imagenet-pretrained-swin-transformer_8xb32-1x1x3-100e_kinetics400-rgb/tsn_imagenet-pretrained-swin-transformer_8xb32-1x1x3-100e_kinetics400-rgb_20220906-65ed814e.pth

  - Name: tsn_imagenet-pretrained-swin-transformer_32xb8-1x1x8-50e_kinetics400-rgb
    Config: configs/recognition/tsn/custom_backbones/tsn_imagenet-pretrained-swin-transformer_32xb8-1x1x8-50e_kinetics400-rgb.py
    In Collection: TSN
    Metadata:
      Architecture: Swin-base
      Batch Size: 32
      Epochs: 100
      FLOPs: 386.7G
      Parameters: 87.15M
      Pretrained: ImageNet
      Resolution: 224x224
      Training Data: Kinetics-400
      Training Resources: 32 GPUs
    Modality: RGB
    Results:
    - Dataset: Kinetics-400
      Task: Action Recognition
      Metrics:
        Top 1 Accuracy: 79.22
        Top 5 Accuracy: 94.20
    Training Log: https://download.openmmlab.com/mmaction/v1.0/recognition/tsn/tsn_imagenet-pretrained-swin-transformer_32xb8-1x1x8-50e_kinetics400-rgb/tsn_imagenet-pretrained-swin-transformer_32xb8-1x1x8-50e_kinetics400-rgb.log
    Weights: https://download.openmmlab.com/mmaction/v1.0/recognition/tsn/tsn_imagenet-pretrained-swin-transformer_8xb32-1x1x3-100e_kinetics400-rgb/tsn_imagenet-pretrained-swin-transformer_8xb32-1x1x3-100e_kinetics400-rgb_20220906-65ed814e.pth

  - Name: tsn_imagenet-pretrained-mobileone-s4_8xb32-1x1x8-100e_kinetics400-rgb
    Config: configs/recognition/tsn/custom_backbones/tsn_imagenet-pretrained-mobileone-s4_8xb32-1x1x8-100e_kinetics400-rgb.py
    In Collection: TSN
    Metadata:
      Architecture: MobileOne-S4
      Batch Size: 32
      Epochs: 100
      FLOPs: 76G
      Parameters: 13.72M
      Pretrained: ImageNet
      Resolution: 224x224
      Training Data: Kinetics-400
      Training Resources: 8 GPUs
    Modality: RGB
    Results:
    - Dataset: Kinetics-400
      Task: Action Recognition
      Metrics:
        Top 1 Accuracy: 73.65
        Top 5 Accuracy: 91.32
    Training Log: https://download.openmmlab.com/mmaction/v1.0/recognition/tsn/tsn_imagenet-pretrained-mobileone-s4_8xb32-1x1x8-100e_kinetics400-rgb/tsn_imagenet-pretrained-mobileone-s4_8xb32-1x1x8-100e_kinetics400-rgb.log
    Weights: https://download.openmmlab.com/mmaction/v1.0/recognition/tsn/tsn_imagenet-pretrained-mobileone-s4_8xb32-1x1x8-100e_kinetics400-rgb/tsn_imagenet-pretrained-mobileone-s4_8xb32-1x1x8-100e_kinetics400-rgb_20230825-2da3c1f7.pth

  - Name: tsn_imagenet-pretrained-r50_8xb32-1x1x8-50e_sthv2-rgb
    Config: configs/recognition/tsn/tsn_imagenet-pretrained-r50_8xb32-1x1x8-50e_sthv2-rgb.py
    In Collection: TSN
    Metadata:
      Architecture: ResNet50
      Batch Size: 32
      Epochs: 100
      FLOPs: 102.7G
      Parameters: 23.87M
      Pretrained: ImageNet
      Resolution: 224x224
      Training Data: Kinetics-400
      Training Resources: 8 GPUs
    Modality: RGB
    Results:
    - Dataset: Kinetics-400
      Task: Action Recognition
      Metrics:
        Top 1 Accuracy: 35.51
        Top 5 Accuracy: 67.09
    Training Log: https://download.openmmlab.com/mmaction/v1.0/recognition/tsn/tsn_imagenet-pretrained-r50_8xb32-1x1x8-50e_sthv2-rgb/tsn_imagenet-pretrained-r50_8xb32-1x1x8-50e_sthv2-rgb.log
    Weights: https://download.openmmlab.com/mmaction/v1.0/recognition/tsn/tsn_imagenet-pretrained-r50_8xb32-1x1x8-50e_sthv2-rgb/tsn_imagenet-pretrained-r50_8xb32-1x1x8-50e_sthv2-rgb_20230313-06ad7d03.pth

  - Name: tsn_imagenet-pretrained-r50_8xb32-1x1x16-50e_sthv2-rgb
    Config: configs/recognition/tsn/tsn_imagenet-pretrained-r50_8xb32-1x1x16-50e_sthv2-rgb.py
    In Collection: TSN
    Metadata:
      Architecture: ResNet50
      Batch Size: 32
      Epochs: 100
      FLOPs: 102.7G
      Parameters: 23.87M
      Pretrained: ImageNet
      Resolution: 224x224
      Training Data: Kinetics-400
      Training Resources: 8 GPUs
    Modality: RGB
    Results:
    - Dataset: Kinetics-400
      Task: Action Recognition
      Metrics:
        Top 1 Accuracy: 36.91
        Top 5 Accuracy: 68.77
    Training Log: https://download.openmmlab.com/mmaction/v1.0/recognition/tsn/tsn_imagenet-pretrained-r50_8xb32-1x1x16-50e_sthv2-rgb/tsn_imagenet-pretrained-r50_8xb32-1x1x16-50e_sthv2-rgb.log
    Weights: https://download.openmmlab.com/mmaction/v1.0/recognition/tsn/tsn_imagenet-pretrained-r50_8xb32-1x1x16-50e_sthv2-rgb/tsn_imagenet-pretrained-r50_8xb32-1x1x16-50e_sthv2-rgb_20230221-85bcc1c3.pth
