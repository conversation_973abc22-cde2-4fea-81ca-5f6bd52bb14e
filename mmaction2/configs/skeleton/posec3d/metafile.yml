Collections:
  - Name: PoseC3D
    README: configs/skeleton/posec3d/README.md
    Paper:
      URL: https://arxiv.org/abs/2104.13586
      Title: "Revisiting Skeleton-based Action Recognition"

Models:
  - Name: slowonly_r50_8xb16-u48-240e_gym-keypoint
    Config: configs/skeleton/posec3d/slowonly_r50_8xb16-u48-240e_gym-keypoint.py
    In Collection: PoseC3D
    Metadata:
      Architecture: SlowOnly-R50
      Batch Size: 16
      Epochs: 240
      FLOPs: 20.6G
      Parameters: 2.0M
      Training Data: FineGYM
      Training Resources: 8 GPUs
      pseudo heatmap: keypoint
    Results:
    - Dataset: FineGYM
      Task: Skeleton-based Action Recognition
      Metrics:
        mean Top 1 Accuracy: 93.5
    Training Log: https://download.openmmlab.com/mmaction/v1.0/skeleton/posec3d/slowonly_r50_8xb16-u48-240e_gym-keypoint/slowonly_r50_8xb16-u48-240e_gym-keypoint.log
    Weights: https://download.openmmlab.com/mmaction/v1.0/skeleton/posec3d/slowonly_r50_8xb16-u48-240e_gym-keypoint/slowonly_r50_8xb16-u48-240e_gym-keypoint_20220815-da338c58.pth

  - Name: slowonly_r50_8xb16-u48-240e_gym-limb
    Config: configs/skeleton/posec3d/slowonly_r50_8xb16-u48-240e_gym-limb.py
    In Collection: PoseC3D
    Metadata:
      Architecture: SlowOnly-R50
      Batch Size: 16
      Epochs: 240
      FLOPs: 20.6G
      Parameters: 2.0M
      Training Data: FineGYM
      Training Resources: 8 GPUs
      pseudo heatmap: limb
    Results:
    - Dataset: FineGYM
      Task: Skeleton-based Action Recognition
      Metrics:
        mean Top 1 Accuracy: 93.6
    Training Log: https://download.openmmlab.com/mmaction/v1.0/skeleton/posec3d/slowonly_r50_8xb16-u48-240e_gym-limb/slowonly_r50_8xb16-u48-240e_gym-limb.log
    Weights: https://download.openmmlab.com/mmaction/v1.0/skeleton/posec3d/slowonly_r50_8xb16-u48-240e_gym-limb/slowonly_r50_8xb16-u48-240e_gym-limb_20220815-2e6e3c5c.pth

  - Name: slowonly_r50_8xb16-u48-240e_ntu60-xsub-keypoint
    Config: configs/skeleton/posec3d/slowonly_r50_8xb16-u48-240e_ntu60-xsub-keypoint.py
    In Collection: PoseC3D
    Metadata:
      Architecture: SlowOnly-R50
      Batch Size: 16
      Epochs: 240
      FLOPs: 20.6G
      Parameters: 2.0M
      Training Data: NTU60-XSub
      Training Resources: 8 GPUs
      pseudo heatmap: keypoint
    Results:
    - Dataset: NTU60-XSub
      Task: Skeleton-based Action Recognition
      Metrics:
        Top 1 Accuracy: 93.6
    Training Log: https://download.openmmlab.com/mmaction/v1.0/skeleton/posec3d/slowonly_r50_8xb16-u48-240e_ntu60-xsub-keypoint/slowonly_r50_8xb16-u48-240e_ntu60-xsub-keypoint.log
    Weights: https://download.openmmlab.com/mmaction/v1.0/skeleton/posec3d/slowonly_r50_8xb16-u48-240e_ntu60-xsub-keypoint/slowonly_r50_8xb16-u48-240e_ntu60-xsub-keypoint_20220815-38db104b.pth

  - Name: slowonly_r50_8xb16-u48-240e_ntu60-xsub-limb
    Config: configs/skeleton/posec3d/slowonly_r50_8xb16-u48-240e_ntu60-xsub-limb.py
    In Collection: PoseC3D
    Metadata:
      Architecture: SlowOnly-R50
      Batch Size: 16
      Epochs: 240
      FLOPs: 20.6G
      Parameters: 2.0M
      Training Data: NTU60-XSub
      Training Resources: 8 GPUs
      pseudo heatmap: limb
    Results:
    - Dataset: NTU60-XSub
      Task: Skeleton-based Action Recognition
      Metrics:
        Top 1 Accuracy: 93.5
    Training Log: https://download.openmmlab.com/mmaction/v1.0/skeleton/posec3d/slowonly_r50_8xb16-u48-240e_ntu60-xsub-limb/slowonly_r50_8xb16-u48-240e_ntu60-xsub-limb.log
    Weights: https://download.openmmlab.com/mmaction/v1.0/skeleton/posec3d/slowonly_r50_8xb16-u48-240e_ntu60-xsub-limb/slowonly_r50_8xb16-u48-240e_ntu60-xsub-limb_20220815-af2f119a.pth

  - Name: slowonly_kinetics400-pretrained-r50_8xb16-u48-120e_hmdb51-split1-keypoint
    Config: configs/skeleton/posec3d/slowonly_kinetics400-pretrained-r50_8xb16-u48-120e_hmdb51-split1-keypoint.py
    In Collection: PoseC3D
    Metadata:
      Architecture: SlowOnly-R50
      Batch Size: 16
      Epochs: 120
      FLOPs: 14.6G
      Parameters: 3.0M
      Training Data: HMDB51
      Training Resources: 8 GPUs
      pseudo heatmap: keypoint
    Results:
    - Dataset: HMDB51
      Task: Skeleton-based Action Recognition
      Metrics:
        Top 1 Accuracy: 69.6
    Training Log: https://download.openmmlab.com/mmaction/v1.0/skeleton/posec3d/slowonly_kinetics400-pretrained-r50_8xb16-u48-120e_hmdb51-split1-keypoint/slowonly_kinetics400-pretrained-r50_8xb16-u48-120e_hmdb51-split1-keypoint.log
    Weights: https://download.openmmlab.com/mmaction/v1.0/skeleton/posec3d/slowonly_kinetics400-pretrained-r50_8xb16-u48-120e_hmdb51-split1-keypoint/slowonly_kinetics400-pretrained-r50_8xb16-u48-120e_hmdb51-split1-keypoint_20220815-17eaa484.pth

  - Name: slowonly_kinetics400-pretrained-r50_8xb16-u48-120e_ucf101-split1-keypoint
    Config: configs/skeleton/posec3d/slowonly_kinetics400-pretrained-r50_8xb16-u48-120e_ucf101-split1-keypoint.py
    In Collection: PoseC3D
    Metadata:
      Architecture: SlowOnly-R50
      Batch Size: 16
      Epochs: 120
      FLOPs: 14.6G
      Parameters: 3.1M
      Training Data: UCF101
      Training Resources: 8 GPUs
      pseudo heatmap: keypoint
    Results:
    - Dataset: UCF101
      Task: Skeleton-based Action Recognition
      Metrics:
        Top 1 Accuracy: 86.8
    Training Log: https://download.openmmlab.com/mmaction/v1.0/skeleton/posec3d/slowonly_kinetics400-pretrained-r50_8xb16-u48-120e_ucf101-split1-keypoint/slowonly_kinetics400-pretrained-r50_8xb16-u48-120e_ucf101-split1-keypoint.log
    Weights: https://download.openmmlab.com/mmaction/v1.0/skeleton/posec3d/slowonly_kinetics400-pretrained-r50_8xb16-u48-120e_ucf101-split1-keypoint/slowonly_kinetics400-pretrained-r50_8xb16-u48-120e_ucf101-split1-keypoint_20220815-9972260d.pth

  - Name: slowonly_r50_8xb32-u48-240e_k400-keypoint
    Config: configs/skeleton/posec3d/slowonly_r50_8xb32-u48-240e_k400-keypoint.py
    In Collection: PoseC3D
    Metadata:
      Architecture: SlowOnly-R50
      Batch Size: 32
      Epochs: 240
      FLOPs: 19.1G
      Parameters: 3.2M
      Training Data: Kinetic400
      Training Resources: 8 GPUs
      pseudo heatmap: keypoint
    Results:
      - Dataset: Kinetic400
        Task: Skeleton-based Action Recognition
        Metrics:
          Top 1 Accuracy: 47.4
    Training Log: https://download.openmmlab.com/mmaction/v1.0/skeleton/posec3d/slowonly_r50_8xb32-u48-240e_k400-keypoint/slowonly_r50_8xb32-u48-240e_k400-keypoint.log
    Weights: https://download.openmmlab.com/mmaction/v1.0/skeleton/posec3d/slowonly_r50_8xb32-u48-240e_k400-keypoint/slowonly_r50_8xb32-u48-240e_k400-keypoint_20230731-7f498b55.pth
