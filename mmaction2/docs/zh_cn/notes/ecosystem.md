# 基于 MMAction2 的生态项目

有许多研究工作和项目是基于 MMAction2 构建的。
我们列举了一些例子，展示了如何扩展 MMAction2 来适用于您自己的项目。
由于页面可能尚未完成，所以请随时通过提交PR来更新此页面。

## 作为扩展的项目

- [OTEAction2](https://github.com/openvinotoolkit/mmaction2)：用于动作识别的 OpenVINO 训练扩展。
- [PYSKL](https://github.com/kennymckormick/pyskl)：一个专注于基于骨骼点动作识别的工具箱。

## 论文相关的项目

还有一些与论文一起发布的项目。
其中一些论文发表在顶级会议（CVPR、ICCV 和 ECCV）上，其他一些也具有很高的影响力。
我们按照会议时间列出它们，方便社区参考。

- Video Swin Transformer，CVPR 2022 [\[论文\]](https://arxiv.org/abs/2106.13230)[\[github\]](https://github.com/SwinTransformer/Video-Swin-Transformer)
- Evidential Deep Learning for Open Set Action Recognition，ICCV 2021 Oral [\[论文\]](https://arxiv.org/abs/2107.10161)[\[github\]](https://github.com/Cogito2012/DEAR)
- Rethinking Self-supervised Correspondence Learning: A Video Frame-level Similarity Perspective，ICCV 2021 Oral [\[论文\]](https://arxiv.org/abs/2103.17263)[\[github\]](https://github.com/xvjiarui/VFS)
- MGSampler: An Explainable Sampling Strategy for Video Action Recognition，ICCV 2021 [\[论文\]](https://arxiv.org/abs/2104.09952)[\[github\]](https://github.com/MCG-NJU/MGSampler)
- MultiSports: A Multi-Person Video Dataset of Spatio-Temporally Localized Sports Actions，ICCV 2021 [\[论文\]](https://arxiv.org/abs/2105.07404)
- Long Short-Term Transformer for Online Action Detection，NeurIPS 2021 [\[论文\]](https://arxiv.org/abs/2107.03377)[\[github\]](https://github.com/amazon-research/long-short-term-transformer)
