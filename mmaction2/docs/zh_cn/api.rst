mmaction.apis
--------------
.. automodule:: mmaction.apis
    :members:

mmaction.datasets
--------------

datasets
^^^^^^^^^^
.. automodule:: mmaction.datasets
    :members:

transforms
^^^^^^^^^^^^
.. automodule:: mmaction.datasets.transforms
    :members:

mmaction.engine
--------------

hooks
^^^^^^^^^^
.. automodule:: mmaction.engine.hooks
    :members:

optimizers
^^^^^^^^^^^^^^^
.. automodule:: mmaction.engine.optimizers
    :members:

runner
^^^^^^^^^^
.. automodule:: mmaction.engine.runner
    :members:


mmaction.evaluation
--------------------

functional
^^^^^^^^^^^^^^^^^
.. automodule:: mmaction.evaluation.functional
    :members:

metrics
^^^^^^^^^^
.. automodule:: mmaction.evaluation.metrics
    :members:


mmaction.models
--------------

backbones
^^^^^^^^^^^^^^^^^^
.. automodule:: mmaction.models.backbones
    :members:

common
^^^^^^^^^^^^^^^^^^
.. automodule:: mmaction.models.common
    :members:

data_preprocessors
^^^^^^^^^^^^^^^^^^^^^^^^^^
.. automodule:: mmaction.models.data_preprocessors
    :members:

heads
^^^^^^^^^^^^^^^
.. automodule:: mmaction.models.heads
    :members:

localizers
^^^^^^^^^^
.. automodule:: mmaction.models.localizers
    :members:


losses
^^^^^^^^^^
.. automodule:: mmaction.models.losses
    :members:

necks
^^^^^^^^^^^^
.. automodule:: mmaction.models.necks
    :members:

roi_heads
^^^^^^^^^^^^^
.. automodule:: mmaction.models.roi_heads
    :members:

recognizers
^^^^^^^^^^^^^
.. automodule:: mmaction.models.seg_heads
    :members:

task_modules
^^^^^^^^^^^^^
.. automodule:: mmaction.models.task_modules
    :members:


utils
^^^^^^^^^^
.. automodule:: mmaction.models.utils
    :members:


mmaction.structures
--------------------

structures
^^^^^^^^^^^^^^^^^
.. automodule:: mmaction.structures
    :members:

bbox
^^^^^^^^^^
.. automodule:: mmaction.structures.bbox
    :members:


mmaction.testing
----------------
.. automodule:: mmaction.testing
    :members:

mmaction.visualization
--------------------
.. automodule:: mmaction.visualization
    :members:

mmaction.utils
--------------
.. automodule:: mmaction.utils
    :members:
