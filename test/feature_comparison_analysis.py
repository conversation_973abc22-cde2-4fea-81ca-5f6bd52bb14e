#!/usr/bin/env python3
"""
特征对比分析脚本
对比推理结果和训练数据的特征一致性
"""

import pandas as pd
import numpy as np
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

def load_and_analyze_features():
    """加载并分析两个特征文件"""
    
    # 文件路径
    inference_file = "/home/<USER>/johnny_ws/mmaction2_ws/data_process/inference/scripts/inference_results/features/20250728T075409Z_20250728T075909Z_segment_000_0.0s-60.0s_resampled.csv"
    training_file = "/home/<USER>/johnny_ws/mmaction2_ws/data/MultiClassTAD/features_slowonly_reconstructed/20250728T075409Z_20250728T075909Z.csv"
    
    print("=" * 80)
    print("特征对比分析报告")
    print("=" * 80)
    
    # 检查文件是否存在
    if not Path(inference_file).exists():
        print(f"错误：推理特征文件不存在: {inference_file}")
        return
    if not Path(training_file).exists():
        print(f"错误：训练特征文件不存在: {training_file}")
        return
    
    # 加载数据
    print("1. 加载数据...")
    try:
        inference_df = pd.read_csv(inference_file)
        training_df = pd.read_csv(training_file)
        print(f"   推理特征文件加载成功: {inference_df.shape}")
        print(f"   训练特征文件加载成功: {training_df.shape}")
    except Exception as e:
        print(f"错误：加载文件失败: {e}")
        return
    
    # 基本信息对比
    print("\n2. 基本信息对比:")
    print(f"   推理特征形状: {inference_df.shape}")
    print(f"   训练特征形状: {training_df.shape}")
    print(f"   行数差异: {abs(inference_df.shape[0] - training_df.shape[0])}")
    print(f"   列数差异: {abs(inference_df.shape[1] - training_df.shape[1])}")
    
    # 列名对比
    print("\n3. 列名对比:")
    inference_cols = list(inference_df.columns)
    training_cols = list(training_df.columns)
    
    print(f"   推理特征列名前10个: {inference_cols[:10]}")
    print(f"   训练特征列名前10个: {training_cols[:10]}")
    
    if inference_cols == training_cols:
        print("   ✓ 列名完全一致")
    else:
        print("   ✗ 列名不一致")
        if len(inference_cols) != len(training_cols):
            print(f"     列数不同: 推理{len(inference_cols)} vs 训练{len(training_cols)}")
        else:
            diff_cols = [(i, inf, tra) for i, (inf, tra) in enumerate(zip(inference_cols, training_cols)) if inf != tra]
            if diff_cols:
                print(f"     不同的列: {diff_cols[:5]}...")  # 只显示前5个
    
    # 数据类型对比
    print("\n4. 数据类型对比:")
    print(f"   推理特征数据类型: {inference_df.dtypes.value_counts().to_dict()}")
    print(f"   训练特征数据类型: {training_df.dtypes.value_counts().to_dict()}")
    
    # 数值范围对比
    print("\n5. 数值范围对比:")
    
    # 获取数值列
    inference_numeric = inference_df.select_dtypes(include=[np.number])
    training_numeric = training_df.select_dtypes(include=[np.number])
    
    print(f"   推理特征数值范围:")
    print(f"     最小值: {inference_numeric.min().min():.6f}")
    print(f"     最大值: {inference_numeric.max().max():.6f}")
    print(f"     均值: {inference_numeric.mean().mean():.6f}")
    print(f"     标准差: {inference_numeric.std().mean():.6f}")
    
    print(f"   训练特征数值范围:")
    print(f"     最小值: {training_numeric.min().min():.6f}")
    print(f"     最大值: {training_numeric.max().max():.6f}")
    print(f"     均值: {training_numeric.mean().mean():.6f}")
    print(f"     标准差: {training_numeric.std().mean():.6f}")
    
    # 统计特性对比（前10列）
    print("\n6. 前10列统计特性对比:")
    common_cols = min(10, len(inference_cols), len(training_cols))
    
    for i in range(common_cols):
        col_inf = inference_cols[i]
        col_tra = training_cols[i]
        
        if col_inf in inference_df.columns and col_tra in training_df.columns:
            inf_data = inference_df[col_inf]
            tra_data = training_df[col_tra]
            
            if pd.api.types.is_numeric_dtype(inf_data) and pd.api.types.is_numeric_dtype(tra_data):
                print(f"   列 {i+1} ({col_inf} vs {col_tra}):")
                print(f"     推理: 均值={inf_data.mean():.6f}, 标准差={inf_data.std():.6f}, 范围=[{inf_data.min():.6f}, {inf_data.max():.6f}]")
                print(f"     训练: 均值={tra_data.mean():.6f}, 标准差={tra_data.std():.6f}, 范围=[{tra_data.min():.6f}, {tra_data.max():.6f}]")
                
                # 计算差异
                mean_diff = abs(inf_data.mean() - tra_data.mean())
                std_diff = abs(inf_data.std() - tra_data.std())
                print(f"     差异: 均值差={mean_diff:.6f}, 标准差差={std_diff:.6f}")
                
                if mean_diff > 0.1 or std_diff > 0.1:
                    print(f"     ⚠️  显著差异!")
                else:
                    print(f"     ✓ 差异较小")
    
    # 相关性分析（如果行数相同）
    if inference_df.shape[0] == training_df.shape[0] and inference_df.shape[1] == training_df.shape[1]:
        print("\n7. 相关性分析:")
        
        # 计算整体相关性
        try:
            # 只取数值列进行相关性计算
            inf_values = inference_numeric.values.flatten()
            tra_values = training_numeric.values.flatten()
            
            correlation = np.corrcoef(inf_values, tra_values)[0, 1]
            print(f"   整体相关系数: {correlation:.6f}")
            
            if correlation > 0.9:
                print("   ✓ 高度相关")
            elif correlation > 0.7:
                print("   ⚠️  中等相关")
            else:
                print("   ✗ 低相关性")
                
        except Exception as e:
            print(f"   相关性计算失败: {e}")
    
    # 数据分布对比（前5列）
    print("\n8. 数据分布对比（前5列）:")
    for i in range(min(5, common_cols)):
        col_inf = inference_cols[i]
        col_tra = training_cols[i]
        
        if col_inf in inference_df.columns and col_tra in training_df.columns:
            inf_data = inference_df[col_inf]
            tra_data = training_df[col_tra]
            
            if pd.api.types.is_numeric_dtype(inf_data) and pd.api.types.is_numeric_dtype(tra_data):
                # 计算分位数
                inf_q25, inf_q50, inf_q75 = inf_data.quantile([0.25, 0.5, 0.75])
                tra_q25, tra_q50, tra_q75 = tra_data.quantile([0.25, 0.5, 0.75])
                
                print(f"   列 {i+1} ({col_inf}):")
                print(f"     推理分位数: Q25={inf_q25:.6f}, Q50={inf_q50:.6f}, Q75={inf_q75:.6f}")
                print(f"     训练分位数: Q25={tra_q25:.6f}, Q50={tra_q50:.6f}, Q75={tra_q75:.6f}")
    
    # 异常值检测
    print("\n9. 异常值检测:")
    
    # 检查推理特征中的异常值
    inf_outliers = 0
    tra_outliers = 0
    
    for col in inference_numeric.columns[:10]:  # 只检查前10列
        Q1 = inference_numeric[col].quantile(0.25)
        Q3 = inference_numeric[col].quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        outliers = ((inference_numeric[col] < lower_bound) | (inference_numeric[col] > upper_bound)).sum()
        inf_outliers += outliers
    
    for col in training_numeric.columns[:10]:  # 只检查前10列
        Q1 = training_numeric[col].quantile(0.25)
        Q3 = training_numeric[col].quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        outliers = ((training_numeric[col] < lower_bound) | (training_numeric[col] > upper_bound)).sum()
        tra_outliers += outliers
    
    print(f"   推理特征异常值数量（前10列）: {inf_outliers}")
    print(f"   训练特征异常值数量（前10列）: {tra_outliers}")
    
    # 总结
    print("\n" + "=" * 80)
    print("总结:")
    print("=" * 80)
    
    issues = []
    
    if inference_df.shape != training_df.shape:
        issues.append("数据形状不一致")
    
    if inference_cols != training_cols:
        issues.append("列名不一致")
    
    # 检查数值范围差异
    inf_mean = inference_numeric.mean().mean()
    tra_mean = training_numeric.mean().mean()
    if abs(inf_mean - tra_mean) > 0.1:
        issues.append("整体均值差异较大")
    
    inf_std = inference_numeric.std().mean()
    tra_std = training_numeric.std().mean()
    if abs(inf_std - tra_std) > 0.1:
        issues.append("整体标准差差异较大")
    
    if issues:
        print("发现的问题:")
        for i, issue in enumerate(issues, 1):
            print(f"  {i}. {issue}")
        print("\n建议检查推理阶段的特征提取代码，确保与训练阶段一致。")
    else:
        print("✓ 特征数据基本一致，未发现明显问题。")
    
    return inference_df, training_df

if __name__ == "__main__":
    load_and_analyze_features()
